#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加朋友窗口处理模块-
功能：激活加好友窗口，输入手机号，点击搜索的自动化操作
作者：AI助手
创建时间：2025-07-25
"""

import sys
import os
import time
import logging
import traceback
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import pandas as pd
import pyautogui
import win32gui
import win32con
import win32api

# 设置标准输出编码为UTF-8，解决Windows下的Unicode问题
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

class WeChatAutoAddFriend:
    """微信自动添加好友类"""
    
    def __init__(self, excel_file: str = "添加好友名单.xlsx"):
        self.excel_file = excel_file
        self.logger = self._setup_logging()
        self.wechat_window = None
        self.add_friend_window = None

        # 🆕 加载配置文件
        self.config = self._load_config()

        # 🆕 合并默认配置（确保向后兼容）
        default_config = {
            "search_input_pos": (1348, 76),      # 搜索输入框坐标
            "search_button_pos": (1469, 73),     # 搜索按钮坐标
            "window_target_pos": (1200, 0),        # 窗口移动目标位置
            "delay_range": (1.5, 3.0),          # 操作延时范围
            "click_delay": 0.5,                 # 点击延时
            "retry_times": 3,                   # 重试次数
            "phone_column": "手机号码",          # 手机号列名
            "status_column": "处理状态",         # 状态列名
            "result_column": "处理结果",         # 结果列名
            "time_column": "处理时间"            # 时间列名
        }

        # 🆕 将默认配置与加载的配置合并
        for key, value in default_config.items():
            if key not in self.config:
                self.config[key] = value
        
        # 统计信息
        self.stats = {
            "total": 0,
            "processed": 0,
            "success": 0,
            "failed": 0,
            "start_time": None,
            "end_time": None
        }

        # 🆕 动态参数缓存
        self._last_config_check = 0
        self._config_cache_timeout = 5  # 5秒缓存超时
        self._cached_runtime_params = None

        # 流程控制标志
        self.auto_start_next_program = False  # 是否自动启动下一个程序
        self.next_program_callback = None     # 下一个程序的回调函数

        # 🔧 新增：暂停控制机制
        self._controller = None  # 主控制器引用，用于检查暂停状态

        # 🔧 关键修复：自动休息配置和状态跟踪
        self.auto_rest_config = self.config.get("auto_rest_config", {
            "rest_trigger": {"friends_count": 1},
            "rest_duration": {"minutes": 1}
        })
        self.rest_counter = 0  # 当前添加成功的好友数量
        self.last_rest_time = None  # 上次休息时间
        self.logger.info(f"✅ 自动休息配置加载: 每{self.auto_rest_config['rest_trigger']['friends_count']}个好友休息{self.auto_rest_config['rest_duration']['minutes']}分钟")

        # 🆕 日志去重机制
        self.log_deduplication = {
            'recent_logs': [],  # 最近的日志记录
            'max_recent_logs': 30,  # 最大保留日志数量
            'duplicate_counts': {},  # 重复日志计数
            'last_log_time': {},  # 最后记录时间
            'suppress_duration': 20  # 重复日志抑制时间（秒）
        }

        # 禁用pyautogui的安全机制
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0.1
        
        self.logger.info("✅ 微信自动添加好友脚本初始化完成")
        self.logger.info(f"📁 Excel文件: {excel_file}")

    def _should_log_message(self, level: str, message: str) -> bool:
        """检查是否应该记录日志消息（去重机制）"""
        try:
            current_time = time.time()

            # 提取日志核心内容
            core_message = self._extract_log_core(message)
            log_key = f"{level}:{core_message}"

            # 检查是否为重复日志
            if log_key in self.log_deduplication['last_log_time']:
                last_time = self.log_deduplication['last_log_time'][log_key]
                time_diff = current_time - last_time

                # 如果在抑制时间内，增加计数但不记录
                if time_diff < self.log_deduplication['suppress_duration']:
                    self.log_deduplication['duplicate_counts'][log_key] = \
                        self.log_deduplication['duplicate_counts'].get(log_key, 0) + 1
                    return False
                else:
                    # 超过抑制时间，记录累积的重复次数
                    duplicate_count = self.log_deduplication['duplicate_counts'].get(log_key, 0)
                    if duplicate_count > 0:
                        self.logger.info(f"📊 上述日志在过去{self.log_deduplication['suppress_duration']}秒内重复了{duplicate_count}次")
                        self.log_deduplication['duplicate_counts'][log_key] = 0

            # 更新最后记录时间
            self.log_deduplication['last_log_time'][log_key] = current_time

            # 添加到最近日志列表
            self.log_deduplication['recent_logs'].append(log_key)

            # 保持最近日志列表大小
            if len(self.log_deduplication['recent_logs']) > self.log_deduplication['max_recent_logs']:
                oldest_key = self.log_deduplication['recent_logs'].pop(0)
                if oldest_key in self.log_deduplication['last_log_time']:
                    del self.log_deduplication['last_log_time'][oldest_key]
                if oldest_key in self.log_deduplication['duplicate_counts']:
                    del self.log_deduplication['duplicate_counts'][oldest_key]

            return True

        except Exception:
            return True

    def _extract_log_core(self, message: str) -> str:
        """提取日志消息的核心内容，去除变量部分"""
        try:
            import re

            # 去除时间戳
            core = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '', message)

            # 去除数字变量
            core = re.sub(r'第 \d+ 个', '第 X 个', core)
            core = re.sub(r'\d+/\d+', 'X/Y', core)
            core = re.sub(r'句柄: \d+', '句柄: X', core)
            core = re.sub(r'窗口 \d+', '窗口 X', core)

            # 去除多余空格
            core = ' '.join(core.split())

            return core.strip()

        except Exception:
            return message

    def _log_with_deduplication(self, level: str, message: str):
        """带去重机制的日志记录"""
        if self._should_log_message(level, message):
            if level.upper() == 'ERROR':
                self.logger.error(message)
            elif level.upper() == 'WARNING':
                self.logger.warning(message)
            elif level.upper() == 'INFO':
                self.logger.info(message)
            elif level.upper() == 'DEBUG':
                self.logger.debug(message)

    def set_controller(self, controller):
        """🔧 设置主控制器引用，用于暂停检查"""
        self._controller = controller
        self.logger.info("✅ 主控制器引用已设置")

    def _check_pause_status(self):
        """🔧 检查暂停状态，如果暂停则等待恢复"""
        if self._controller and hasattr(self._controller, '_is_paused'):
            if self._controller._is_paused:
                self.logger.info("⏸️ 检测到暂停状态，等待恢复...")
                return self._controller._wait_while_paused()
        return True

    def _sleep_with_pause_check(self, duration, is_auto_rest=False):
        """🔧 支持暂停检查的睡眠方法"""
        if duration <= 0:
            return True

        # 将睡眠时间分割为0.5秒的小段，每段都检查暂停状态
        sleep_interval = 0.5
        remaining_time = duration
        total_duration = duration

        while remaining_time > 0:
            # 检查暂停状态
            if not self._check_pause_status():
                return False

            # 🔧 新增：如果是自动休息，更新GUI倒计时显示
            if is_auto_rest and self._controller:
                remaining_minutes = int(remaining_time // 60)
                remaining_seconds = int(remaining_time % 60)
                if remaining_minutes > 0:
                    countdown_text = f"{remaining_minutes}分{remaining_seconds}秒"
                else:
                    countdown_text = f"{remaining_seconds}秒"

                # 更新GUI倒计时
                if hasattr(self._controller, '_notify_gui_countdown'):
                    self._controller._notify_gui_countdown(countdown_text)

            # 睡眠一小段时间
            current_sleep = min(sleep_interval, remaining_time)
            time.sleep(current_sleep)
            remaining_time -= current_sleep

        return True

    def _check_auto_rest(self):
        """🔧 关键修复：检查是否需要自动休息"""
        try:
            rest_trigger_count = self.auto_rest_config.get("rest_trigger", {}).get("friends_count", 1)
            rest_duration_minutes = self.auto_rest_config.get("rest_duration", {}).get("minutes", 1)

            # 检查是否达到休息触发条件
            if self.rest_counter >= rest_trigger_count:
                self.logger.info(f"🛌 达到自动休息触发条件：已成功添加 {self.rest_counter} 个好友")
                self.logger.info(f"⏰ 开始自动休息 {rest_duration_minutes} 分钟...")

                # 更新GUI步骤状态
                if self._controller:
                    self._controller._notify_gui_step(f"自动休息中：已添加{self.rest_counter}个好友，休息{rest_duration_minutes}分钟")

                # 执行休息
                rest_duration_seconds = rest_duration_minutes * 60
                self._sleep_with_pause_check(rest_duration_seconds, is_auto_rest=True)

                # 重置休息计数器
                self.rest_counter = 0
                self.last_rest_time = datetime.now()

                self.logger.info(f"😊 自动休息完成，正在重新扫描微信窗口...")

                # 🔧 关键修复：休息完成后重新验证微信窗口是否存在
                if self._controller:
                    self._controller._notify_gui_step("自动休息完成：正在重新扫描微信窗口...")

                    # 🔧 使用控制器的窗口管理器重新扫描，确保GUI状态同步
                    windows = self._controller.get_wechat_windows()

                    if not windows:
                        # 🆕 使用去重机制记录错误日志
                        self._log_with_deduplication("ERROR", f"❌ 自动休息后未找到微信窗口，终止执行流程")
                        if self._controller:
                            self._controller._notify_gui_step("❌ 未找到微信窗口，流程已终止")
                            # 🔧 关键修复：更新GUI窗口检测状态
                            self._controller._notify_gui_window_detection("❌ 未找到窗口")
                        return "NO_WINDOWS_FOUND"  # 返回特殊状态码

                    self.logger.info(f"✅ 自动休息后重新扫描到 {len(windows)} 个微信窗口，继续执行")
                    if self._controller:
                        self._controller._notify_gui_step(f"✅ 重新扫描到{len(windows)}个微信窗口，继续执行添加好友流程")
                        # 🔧 关键修复：更新GUI窗口检测状态
                        self._controller._notify_gui_window_detection(f"✅ 已检测到{len(windows)}个窗口")

                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 自动休息检查失败: {e}")
            return False

    def _trigger_auto_rest(self):
        """🔧 关键修复：触发自动休息（成功添加好友后调用）"""
        try:
            self.rest_counter += 1
            self.logger.debug(f"📊 自动休息计数器更新: {self.rest_counter}")

            # 检查是否需要休息
            rest_result = self._check_auto_rest()

            # 🔧 关键修复：传递特殊状态码
            if rest_result == "NO_WINDOWS_FOUND":
                return "NO_WINDOWS_FOUND"

            return rest_result

        except Exception as e:
            self.logger.error(f"❌ 触发自动休息失败: {e}")
            return False

    def _load_config(self):
        """加载配置文件"""
        config_file = "config.json"
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.logger.info(f"✅ 配置文件加载成功: {config_file}")
                return config
        except FileNotFoundError:
            self.logger.warning(f"⚠️ 配置文件未找到: {config_file}，使用默认配置")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ 配置文件格式错误: {e}，使用默认配置")
            return {}
        except UnicodeDecodeError as e:
            self.logger.error(f"❌ 配置文件编码错误: {e}，使用默认配置")
            return {}
        except Exception as e:
            self.logger.error(f"❌ 配置文件加载失败: {e}，使用默认配置")
            return {}

    def _get_realtime_runtime_params(self):
        """🆕 动态参数响应 - 实时读取GUI设置的运行参数"""
        import time
        current_time = time.time()

        # 检查缓存是否过期
        if (current_time - self._last_config_check < self._config_cache_timeout and
            self._cached_runtime_params is not None):
            return self._cached_runtime_params

        try:
            # 重新读取配置文件
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            runtime_params = config.get("runtime_parameters", {})

            # 更新缓存
            self._cached_runtime_params = runtime_params
            self._last_config_check = current_time

            self.logger.debug(f"🔄 实时参数更新：每日限制={runtime_params.get('daily_add_limit', {}).get('value')}，单窗口限制={runtime_params.get('max_adds_per_window', {}).get('value')}")

            return runtime_params

        except Exception as e:
            self.logger.warning(f"⚠️ 实时读取运行参数失败: {e}，使用缓存或默认值")
            # 返回缓存的参数或空字典
            return self._cached_runtime_params or {}

    def set_auto_start_next_program(self, callback_function=None):
        """设置自动启动下一个程序的功能

        Args:
            callback_function: 下一个程序的回调函数，如果为None则使用默认的启动方式
        """
        self.auto_start_next_program = True
        self.next_program_callback = callback_function
        self.logger.info("✅ 已启用自动启动下一个程序功能")

    def disable_auto_start_next_program(self):
        """禁用自动启动下一个程序的功能"""
        self.auto_start_next_program = False
        self.next_program_callback = None
        self.logger.info("❌ 已禁用自动启动下一个程序功能")
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志
        log_file = log_dir / f"wechat_auto_simple_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info(f"📝 日志文件: {log_file}")
        return logger
    
    def find_wechat_windows(self) -> List[Dict]:
        """查找微信窗口（支持添加朋友窗口和主窗口）"""
        windows = []

        def enum_windows_callback(hwnd, windows_list):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # 确保class_name不为None
                if class_name is None:
                    class_name = ""

                # 检查是否为微信窗口（添加朋友窗口或主窗口）
                if (self._is_real_wechat_window(window_title, class_name)):
                    rect = win32gui.GetWindowRect(hwnd)
                    window_width = rect[2] - rect[0]
                    window_height = rect[3] - rect[1]

                    # 分类窗口类型
                    window_type = self._classify_window_type(window_title, class_name, window_width, window_height)

                    # 只添加识别出的微信窗口
                    if window_type != "unknown":
                        windows_list.append({
                            "hwnd": hwnd,
                            "title": window_title,
                            "class": class_name,
                            "rect": rect,
                            "width": window_width,
                            "height": window_height,
                            "type": window_type
                        })
                        self.logger.debug(f"🎯 找到微信窗口: '{window_title}' ({window_width}x{window_height}) - {window_type}")

        win32gui.EnumWindows(enum_windows_callback, windows)

        # 统计窗口类型
        add_friend_count = sum(1 for w in windows if w['type'] == 'add_friend')
        main_count = sum(1 for w in windows if w['type'] == 'main')

        self.logger.info(f"🎯 找到 {len(windows)} 个微信窗口:")
        self.logger.info(f"   📊 添加朋友窗口: {add_friend_count} 个")
        self.logger.info(f"   📊 主窗口: {main_count} 个")

        for i, window in enumerate(windows):
            self.logger.info(f"  {i+1}. {window['title']} ({window['width']}x{window['height']}) - {window['type']}")

        return windows

    def _is_real_wechat_window(self, window_title: str, class_name: str) -> bool:
        """判断是否为微信窗口（支持添加朋友窗口和主窗口）"""
        # 排除我们的GUI程序和其他干扰窗口
        if ("微信自动化系统" in window_title or
            "Visual Studio Code" in window_title or
            "PyCharm" in window_title or
            "Notepad" in window_title):
            return False

        # 检测微信相关窗口
        # 1. 添加朋友窗口：类名为Qt51514QWindowIcon，标题包含"添加朋友"
        if (class_name == "Qt51514QWindowIcon" and "添加朋友" in window_title):
            return True

        # 2. 微信主窗口：类名为Qt51514QWindowIcon，标题为"微信"
        if (class_name == "Qt51514QWindowIcon" and window_title == "微信"):
            return True

        # 3. 传统微信主窗口：类名为WeChatMainWndForPC
        if (class_name == "WeChatMainWndForPC"):
            return True

        return False

    def _classify_window_type(self, title: str, class_name: str, width: int, height: int) -> str:
        """分类窗口类型（支持添加朋友窗口和主窗口）"""
        # 1. 添加朋友窗口：标题包含"添加朋友"，大小约为328x454
        if "添加朋友" in title and class_name == "Qt51514QWindowIcon":
            # 验证窗口大小是否符合添加朋友窗口特征（328x454像素）
            # 允许一定的误差范围（±20像素）
            if (308 <= width <= 348 and 434 <= height <= 474):
                return "add_friend"

        # 2. 微信主窗口：标题为"微信"，大小较大
        if title == "微信" and class_name == "Qt51514QWindowIcon":
            # 主窗口通常比较大，至少600x400
            if width >= 600 and height >= 400:
                return "main"

        # 3. 传统微信主窗口
        if class_name == "WeChatMainWndForPC":
            return "main"

        return "unknown"
    
    def activate_window(self, hwnd: int) -> bool:
        """激活指定窗口（增强重试版）- 集成新的窗口管理功能"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 检查窗口是否存在
                if not win32gui.IsWindow(hwnd):
                    self.logger.error("❌ 窗口句柄无效")
                    return False

                # 使用增强的窗口管理器（激活+置顶）
                try:
                    from modules.window_manager import WeChatWindowManager
                    window_manager = WeChatWindowManager()

                    # 🔧 使用增强的窗口激活方法：激活+置顶
                    if window_manager.ensure_window_on_top(hwnd):
                        self.logger.info("✅ 使用窗口管理器激活并置顶成功")
                        return True
                    else:
                        self.logger.warning("⚠️ 窗口管理器激活+置顶失败，尝试传统方法")

                except ImportError:
                    self.logger.warning("⚠️ 无法导入窗口管理器，使用传统激活方法")

                # 尝试多种激活方法（增强版：包含置顶）
                try:
                    # 方法1: 标准激活+置顶
                    win32gui.SetForegroundWindow(hwnd)
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    # 🔧 设置窗口置顶
                    win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                    self.logger.info("✅ 传统方法激活+置顶成功")
                except:
                    # 方法2: 强制激活+置顶
                    win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                    win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, 0, 0,
                                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                    win32gui.SetForegroundWindow(hwnd)
                    # 🔧 设置窗口置顶
                    win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                        win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                    self.logger.info("✅ 强制方法激活+置顶成功")

                # 等待窗口激活
                time.sleep(1)

                # 验证窗口是否被激活
                current_hwnd = win32gui.GetForegroundWindow()
                if current_hwnd == hwnd:
                    self.logger.info(f"✅ 窗口激活成功 (第{attempt + 1}次尝试)")
                    return True
                else:
                    self.logger.warning(f"⚠️ 窗口激活失败 (第{attempt + 1}次尝试)")
                    if attempt < max_retries - 1:
                        time.sleep(1)  # 重试前等待
                        continue

            except Exception as e:
                self.logger.error(f"❌ 激活窗口异常 (第{attempt + 1}次尝试): {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 重试前等待
                    continue

        self.logger.error(f"❌ 窗口激活失败，已重试{max_retries}次")
        return False
    
    def move_window_to_position(self, hwnd: int, x: int = 0, y: int = 0) -> bool:
        """将窗口移动到指定位置"""
        try:
            # 获取当前窗口位置和大小
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            # 移动窗口
            win32gui.SetWindowPos(
                hwnd, 
                win32con.HWND_TOP,  # 置顶
                x, y, width, height,
                win32con.SWP_SHOWWINDOW
            )
            
            self.logger.info(f"✅ 窗口已移动到位置 ({x}, {y})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 移动窗口失败: {e}")
            return False
    
    def load_phone_numbers(self) -> Tuple[List[Dict], Dict]:
        """从Excel文件加载手机号码，返回联系人列表和统计信息"""
        try:
            self.logger.info(f"📂 开始加载Excel文件: {self.excel_file}")

            # 检查文件是否存在
            if not os.path.exists(self.excel_file):
                self.logger.error(f"❌ Excel文件不存在: {self.excel_file}")
                return [], {"error": "file_not_found"}

            # 读取Excel文件
            df = pd.read_excel(self.excel_file)
            self.logger.info(f"📊 Excel文件读取成功，共 {len(df)} 行数据")
            self.logger.info(f"📋 列名: {list(df.columns)}")

            # 检查必要的列是否存在
            phone_col = self.config["phone_column"]
            if phone_col not in df.columns:
                self.logger.error(f"❌ 未找到手机号列: {phone_col}")
                return [], {"error": "column_not_found"}

            # 统计所有数据
            total_contacts = 0
            processed_contacts = 0
            pending_contacts = []

            for index, row in df.iterrows():
                phone = str(row[phone_col]).strip()
                status = str(row.get(self.config["status_column"], "")).strip()

                # 跳过空值
                if phone and phone != "nan":
                    total_contacts += 1

                    # 检查是否已处理完成
                    if status in ["成功", "完成"]:
                        processed_contacts += 1
                    # 如果状态为空、待处理、处理中或失败，则加入处理列表
                    elif (not status or status in ["", "nan", "待处理", "处理中", "失败"]):
                        pending_contacts.append({
                            "index": index,
                            "phone": phone,
                            "name": str(row.get("姓名", "")).strip(),
                            "verification": str(row.get("验证信息", "你好")).strip(),
                            "current_status": status
                        })

            # 生成统计信息
            stats = {
                "total": total_contacts,
                "processed": processed_contacts,
                "pending": len(pending_contacts),
                "all_completed": (total_contacts > 0 and len(pending_contacts) == 0)
            }

            self.logger.info(f"✅ 加载完成，待处理联系人: {len(pending_contacts)} 个")
            if stats["all_completed"]:
                self.logger.info(f"🎉 所有联系人已处理完成！总计: {total_contacts} 个，已完成: {processed_contacts} 个")

            return pending_contacts, stats

        except Exception as e:
            self.logger.error(f"❌ 加载Excel文件失败: {e}")
            return [], {"error": "load_failed", "message": str(e)}
    
    def update_excel_status(self, index: int, status: str, result: str):
        """更新Excel文件中的处理状态"""
        try:
            # 读取Excel文件
            df = pd.read_excel(self.excel_file)

            # 状态映射 - 简化为只有成功和失败两种状态
            status_mapping = {
                "成功": "成功",
                "success": "成功",
                "completed": "成功",
                "add_to_contacts": "成功",
                "已添加到通讯录": "成功",
                "好友申请已发送": "成功"
            }

            # 处理结果映射 - 根据用户要求的4种具体结果
            result_mapping = {
                "检测到操作频繁": "检测到操作频繁",
                "添加朋友成功": "添加朋友成功",
                "已是共同好友": "已是共同好友",
                "无法添加好友": "无法添加好友"
            }

            # 标准化状态和结果
            final_status = status_mapping.get(status, "失败")  # 默认失败状态
            final_result = result_mapping.get(result, result)  # 保持原结果或使用映射

            # 使用北京时间
            from datetime import timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = datetime.now(beijing_tz).strftime("%Y-%m-%d %H:%M:%S")

            # 更新状态
            df.loc[index, self.config["status_column"]] = final_status
            df.loc[index, self.config["result_column"]] = final_result
            df.loc[index, self.config["time_column"]] = beijing_time

            # 保存文件
            df.to_excel(self.excel_file, index=False)
            self.logger.debug(f"📝 更新Excel状态: 行{index+1} - {final_status}: {final_result}")

        except Exception as e:
            self.logger.error(f"❌ 更新Excel状态失败: {e}")
    
    def clear_search_input(self):
        """清空搜索输入框"""
        try:
            # 点击搜索输入框
            pyautogui.click(self.config["search_input_pos"])
            time.sleep(0.2)
            
            # 全选并删除
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.1)
            pyautogui.press('delete')
            time.sleep(0.2)
            
            self.logger.debug("🧹 搜索输入框已清空")
            
        except Exception as e:
            self.logger.error(f"❌ 清空搜索输入框失败: {e}")
    
    def input_phone_number(self, phone: str):
        """在搜索框中输入手机号"""
        try:
            # 点击搜索输入框
            pyautogui.click(self.config["search_input_pos"])
            time.sleep(0.3)
            
            # 输入手机号
            pyautogui.typewrite(phone, interval=0.05)
            time.sleep(0.5)
            
            self.logger.debug(f"📱 已输入手机号: {phone}")
            
        except Exception as e:
            self.logger.error(f"❌ 输入手机号失败: {e}")
            raise
    
    def click_search_button(self):
        """点击搜索按钮"""
        try:
            pyautogui.click(self.config["search_button_pos"])
            time.sleep(2)  # 等待搜索结果

            self.logger.debug("🔍 已点击搜索按钮")

        except Exception as e:
            self.logger.error(f"❌ 点击搜索按钮失败: {e}")
            raise

    def process_single_contact(self, contact: Dict) -> Dict:
        """处理单个联系人"""
        try:
            phone = contact["phone"]
            name = contact.get("name", "")

            self.logger.info(f"📞 开始处理: {phone} ({name})")

            # 🆕 在处理联系人前检查是否需要重新开始
            try:
                from modules.frequency_error_handler import FrequencyErrorHandler
                frequency_handler = FrequencyErrorHandler(self.logger)
                if frequency_handler.is_restart_required():
                    self.logger.warning("🔄 检测到频率错误处理完成，需要立即停止当前联系人处理")
                    return {
                        "success": False,
                        "status": "restart_required",
                        "message": "频率错误处理完成，需要重新开始流程"
                    }
            except Exception as e:
                self.logger.warning(f"⚠️ 频率错误检测失败: {e}")

            # 1. 清空搜索输入框
            self.clear_search_input()

            # 2. 输入手机号
            self.input_phone_number(phone)

            # 3. 点击搜索按钮
            self.click_search_button()

            # 4. 等待搜索结果并分析
            result = self._analyze_search_result(phone)

            # 🆕 在处理完成后再次检查是否需要重新开始
            try:
                from modules.frequency_error_handler import FrequencyErrorHandler
                frequency_handler = FrequencyErrorHandler(self.logger)
                if frequency_handler.is_restart_required():
                    self.logger.warning("🔄 处理完成后检测到频率错误，需要立即停止")
                    return {
                        "success": False,
                        "status": "restart_required",
                        "message": "频率错误处理完成，需要重新开始流程"
                    }
            except Exception as e:
                self.logger.warning(f"⚠️ 频率错误检测失败: {e}")

            return result

        except Exception as e:
            self.logger.error(f"❌ 处理联系人失败: {phone} - {e}")
            return {
                "success": False,
                "status": "error",
                "message": f"处理异常: {str(e)}"
            }

    def _analyze_search_result(self, phone: str) -> Dict:
        """分析搜索结果并自动执行添加朋友操作"""
        try:
            # 等待页面加载
            time.sleep(2)

            # 截取屏幕进行分析
            screenshot = pyautogui.screenshot()
            self.logger.info(f"🔍 搜索结果分析完成: {phone}")

            # 🚀 新增：自动调用添加朋友模块
            self.logger.info("🔗 搜索完成，开始自动执行添加朋友操作...")
            add_friend_result = self._execute_auto_add_friend(phone)

            if add_friend_result["success"]:
                self.logger.info(f"✅ 完整流程执行成功: {phone}")
                return {
                    "success": True,
                    "status": "completed",
                    "message": f"搜索并添加朋友成功: {add_friend_result['message']}"
                }
            else:
                self.logger.warning(f"⚠️ 添加朋友失败: {phone} - {add_friend_result['message']}")
                return {
                    "success": True,  # 搜索成功，但添加失败
                    "status": "searched_only",
                    "message": f"搜索成功但添加朋友失败: {add_friend_result['message']}"
                }

        except Exception as e:
            self.logger.error(f"❌ 分析搜索结果失败: {e}")
            return {
                "success": False,
                "status": "error",
                "message": f"分析失败: {str(e)}"
            }

    def _execute_auto_add_friend(self, phone: str) -> Dict:
        """执行自动添加朋友操作"""
        try:
            self.logger.info(f"👥 开始为 {phone} 执行添加朋友操作...")

            # 动态导入添加朋友模块（避免初始化时的依赖问题）
            try:
                from modules.wechat_auto_add_friend import WeChatAutoAddFriend
                self.logger.info("📦 成功导入添加朋友模块")
            except ImportError as e:
                self.logger.error(f"❌ 导入添加朋友模块失败: {e}")
                return {
                    "success": False,
                    "message": f"模块导入失败: {str(e)}"
                }

            # 初始化添加朋友实例
            self.logger.info("🔧 正在初始化添加朋友模块...")
            add_friend_instance = WeChatAutoAddFriend(debug_mode=True)

            # 清理截图目录
            self.logger.info("🧹 清理截图目录...")
            add_friend_instance.clean_screenshots_directory()

            # 执行添加朋友操作
            self.logger.info("🚀 启动添加朋友操作...")
            self.logger.info("📋 功能说明:")
            self.logger.info("  - 自动检测微信'添加朋友'窗口")
            self.logger.info("  - 图像识别'添加到通讯录'文字")
            self.logger.info("  - 点击'添加到通讯录'按钮")
            self.logger.info("  - 保存截图和日志到本地")

            # 执行自动添加朋友操作
            success = add_friend_instance.execute_auto_add_friend()

            # 🆕 执行添加朋友操作后立即检查频率错误
            try:
                from modules.frequency_error_handler import FrequencyErrorHandler
                frequency_handler = FrequencyErrorHandler(self.logger)
                if frequency_handler.is_restart_required():
                    self.logger.warning("🔄 添加朋友操作后检测到频率错误，立即停止")
                    return {
                        "success": False,
                        "status": "restart_required",
                        "message": "频率错误处理完成，需要重新开始流程"
                    }
            except Exception as e:
                self.logger.warning(f"⚠️ 频率错误检测失败: {e}")

            if success:
                self.logger.info(f"✅ {phone} 添加朋友操作执行成功")

                # 🆕 添加朋友成功后，再次检查频率错误
                try:
                    from modules.frequency_error_handler import FrequencyErrorHandler
                    frequency_handler = FrequencyErrorHandler(self.logger)
                    if frequency_handler.is_restart_required():
                        self.logger.warning("🔄 添加朋友成功后检测到频率错误，立即停止")
                        return {
                            "success": False,
                            "status": "restart_required",
                            "message": "频率错误处理完成，需要重新开始流程"
                        }
                except Exception as e:
                    self.logger.warning(f"⚠️ 频率错误检测失败: {e}")

                # 🆕 添加朋友成功后，检查并处理申请添加朋友窗口
                self.logger.info("🔍 检查是否出现申请添加朋友窗口...")
                friend_request_result = self._handle_friend_request_window(phone)

                # 🆕 检查好友申请窗口处理结果是否包含频率错误
                if isinstance(friend_request_result, dict) and friend_request_result.get("error_type") == "frequency_error_handled":
                    self.logger.warning("🔄 好友申请窗口处理检测到频率错误，立即停止")
                    return {
                        "success": False,
                        "status": "restart_required",
                        "message": "频率错误处理完成，需要重新开始流程"
                    }

                # 🆕 处理申请窗口后，再次检查频率错误
                try:
                    from modules.frequency_error_handler import FrequencyErrorHandler
                    frequency_handler = FrequencyErrorHandler(self.logger)
                    if frequency_handler.is_restart_required():
                        self.logger.warning("🔄 处理申请窗口后检测到频率错误，立即停止")
                        return {
                            "success": False,
                            "status": "restart_required",
                            "message": "频率错误处理完成，需要重新开始流程"
                        }
                except Exception as e:
                    self.logger.warning(f"⚠️ 频率错误检测失败: {e}")

                if isinstance(friend_request_result, dict) and friend_request_result.get("found"):
                    self.logger.info(f"✅ {phone} 申请添加朋友窗口处理完成: {friend_request_result['message']}")
                elif isinstance(friend_request_result, dict):
                    self.logger.info(f"ℹ️ {phone} 未发现申请添加朋友窗口，可能直接添加成功")
                else:
                    self.logger.info(f"ℹ️ {phone} 申请添加朋友窗口处理完成")

                return {
                    "success": True,
                    "message": f"添加朋友操作完成，申请窗口处理: {friend_request_result['message']}"
                }
            else:
                self.logger.error(f"❌ {phone} 添加朋友操作执行失败")
                return {
                    "success": False,
                    "message": "添加朋友操作失败，可能原因：未找到'添加朋友'窗口或'添加到通讯录'按钮"
                }

        except Exception as e:
            self.logger.error(f"❌ 执行添加朋友操作异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                "success": False,
                "message": f"添加朋友操作异常: {str(e)}"
            }

    def _handle_friend_request_window(self, phone: str) -> Dict:
        """处理申请添加朋友窗口"""
        try:
            # 等待窗口出现
            time.sleep(2)

            # 检查申请添加朋友窗口是否存在
            import win32gui

            request_window_keywords = [
                "申请添加朋友",
                "添加朋友",
                "好友申请",
                "验证信息"
            ]

            found_windows = []

            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if window_title:
                        for keyword in request_window_keywords:
                            if keyword in window_title:
                                windows.append({
                                    'hwnd': hwnd,
                                    'title': window_title
                                })
                                break
                return True

            win32gui.EnumWindows(enum_windows_callback, found_windows)

            if not found_windows:
                return {
                    "found": False,
                    "message": "未发现申请添加朋友窗口"
                }

            # 找到申请添加朋友窗口，调用friend_request_window.py处理
            self.logger.info(f"🎯 找到申请添加朋友窗口: {found_windows[0]['title']}")

            try:
                from modules.friend_request_window import FriendRequestProcessor
                self.logger.info("📦 成功导入申请添加朋友处理模块")

                # 创建处理器实例
                processor = FriendRequestProcessor()

                # 执行好友申请流程
                self.logger.info("🚀 调用申请添加朋友处理模块...")
                result = processor.execute_friend_request_flow(
                    phone=phone,
                    verification_msg=""  # 验证信息将从Excel中读取
                )

                if isinstance(result, dict):
                    if result.get("status") == "success":
                        return {
                            "found": True,
                            "message": "申请添加朋友窗口处理成功"
                        }
                    else:
                        return {
                            "found": True,
                            "message": f"申请添加朋友窗口处理失败: {result.get('message', '未知错误')}"
                        }
                elif result:
                    return {
                        "found": True,
                        "message": "申请添加朋友窗口处理成功"
                    }
                else:
                    return {
                        "found": True,
                        "message": "申请添加朋友窗口处理失败"
                    }

            except ImportError as e:
                self.logger.error(f"❌ 导入申请添加朋友处理模块失败: {e}")
                return {
                    "found": True,
                    "message": f"模块导入失败: {str(e)}"
                }

        except Exception as e:
            self.logger.error(f"❌ 处理申请添加朋友窗口异常: {e}")
            return {
                "found": False,
                "message": f"处理异常: {str(e)}"
            }

    def run_automation(self, start_index: int = 0) -> Union[bool, str]:
        """运行自动化流程

        Args:
            start_index: 开始处理的联系人索引（用于全局进度跟踪）
        """
        try:
            self.stats["start_time"] = datetime.now()
            self.logger.info("🚀 开始执行微信自动添加好友流程...")

            # 🔧 暂停检查点1：开始执行前
            if not self._check_pause_status():
                return False

            # 1. 查找微信窗口
            windows = self.find_wechat_windows()
            if not windows:
                # 🆕 使用去重机制记录错误日志
                self._log_with_deduplication("ERROR", "❌ 未找到微信窗口，请确保微信已启动")
                return False

            # 2. 查找添加朋友窗口
            add_friend_window = None
            main_window = None

            for window in windows:
                if window["type"] == "add_friend":
                    add_friend_window = window
                elif window["type"] == "main":
                    main_window = window

            # 3. 处理窗口状态和激活
            if add_friend_window:
                # 优先使用添加朋友窗口
                target_window = add_friend_window
                self.logger.info(f"🎯 使用添加朋友窗口: {target_window['title']}")
            elif main_window:
                # 如果只有主窗口，需要先打开添加朋友窗口
                target_window = main_window
                self.logger.info(f"🎯 检测到主窗口: {target_window['title']}")
                self.logger.info("🔄 需要先执行主界面操作来打开添加朋友窗口...")

                # 激活主窗口
                if not self.activate_window(target_window["hwnd"]):
                    self.logger.error("❌ 主窗口激活失败")
                    return False

                # 调用主界面操作模块打开添加朋友窗口
                if not self._execute_main_interface_operations():
                    self.logger.error("❌ 主界面操作失败，无法打开添加朋友窗口")
                    return False

                # 重新查找添加朋友窗口
                self.logger.info("🔍 重新查找添加朋友窗口...")
                time.sleep(2)  # 等待窗口打开
                windows = self.find_wechat_windows()
                add_friend_window = None
                for window in windows:
                    if window["type"] == "add_friend":
                        add_friend_window = window
                        break

                if add_friend_window:
                    target_window = add_friend_window
                    self.logger.info(f"✅ 成功打开添加朋友窗口: {target_window['title']}")
                else:
                    self.logger.error("❌ 主界面操作后仍未找到添加朋友窗口")
                    return False
            else:
                # 🆕 使用去重机制记录错误日志
                self._log_with_deduplication("ERROR", "❌ 未找到合适的微信窗口")
                return False

            # 4. 激活并移动目标窗口（增强版）
            if not self.activate_window(target_window["hwnd"]):
                self.logger.error("❌ 窗口激活失败")
                return False

            # 特殊处理添加朋友窗口
            if target_window["type"] == "add_friend":
                self.logger.info("👥 检测到添加朋友窗口，执行专门的处理流程...")
                if not self._handle_add_friend_window_specifically(target_window["hwnd"]):
                    self.logger.warning("⚠️ 添加朋友窗口专门处理失败，继续使用通用流程")

            if not self.move_window_to_position(
                target_window["hwnd"],
                self.config["window_target_pos"][0],
                self.config["window_target_pos"][1]
            ):
                self.logger.warning("⚠️ 窗口移动失败，继续执行")

            # 5. 加载联系人数据
            contacts, load_stats = self.load_phone_numbers()

            # 检查加载结果
            if "error" in load_stats:
                if load_stats["error"] == "file_not_found":
                    self.logger.error("❌ Excel文件不存在")
                elif load_stats["error"] == "column_not_found":
                    self.logger.error("❌ Excel文件格式错误，未找到必要的列")
                else:
                    self.logger.error(f"❌ 加载Excel文件失败: {load_stats.get('message', '未知错误')}")
                return False

            # 检查是否所有数据都已处理完成
            if load_stats.get("all_completed", False):
                self.logger.info("🎉 所有数据已全部处理完成！")
                self.logger.info(f"📊 处理统计: 总计 {load_stats['total']} 个联系人，已完成 {load_stats['processed']} 个")

                # 生成完成报告
                self.stats["total"] = load_stats["total"]
                self.stats["processed"] = load_stats["processed"]
                self.stats["success"] = load_stats["processed"]
                self.stats["failed"] = 0
                self.stats["end_time"] = datetime.now()
                self.generate_completion_report()
                return True

            # 🆕 根据全局索引过滤联系人
            if start_index > 0:
                self.logger.info(f"🔄 全局进度跟踪：从第 {start_index + 1} 个联系人开始处理")
                # 跳过已处理的联系人
                if start_index >= len(contacts):
                    self.logger.info("✅ 当前窗口的联系人已全部处理完成")
                    return True
                contacts = contacts[start_index:]
                self.logger.info(f"📊 当前窗口待处理联系人: {len(contacts)} 个")

            # 如果没有待处理的联系人但也不是全部完成的情况
            if not contacts:
                self.logger.error("❌ 未找到待处理的联系人")
                return False

            self.stats["total"] = load_stats["total"]
            self.logger.info(f"📊 待处理联系人: {len(contacts)} 个 (总计: {load_stats['total']} 个)")

            # 6. 批量处理联系人
            success_count = 0
            failed_count = 0

            # 🆕 动态参数响应 - 实时读取GUI设置的数量限制参数
            runtime_params = self._get_realtime_runtime_params()
            max_per_window = runtime_params.get("max_adds_per_window", {}).get("value", None)
            daily_limit = runtime_params.get("daily_add_limit", {}).get("value", None)

            if max_per_window is not None and max_per_window > 0:
                contacts_per_window = max_per_window
                self.logger.info(f"📋 使用GUI实时设置：每个窗口处理 {contacts_per_window} 个联系人后切换")
            else:
                # 回退到multi_window配置
                contacts_per_window = self.config.get("multi_window", {}).get("contacts_per_window", 10)
                self.logger.info(f"📋 使用默认配置：每个窗口处理 {contacts_per_window} 个联系人后切换")

            # 🆕 参数验证 - 确保数量限制参数有效
            if contacts_per_window <= 0:
                self.logger.error(f"❌ 无效的单窗口限制参数：{contacts_per_window}，使用默认值10")
                contacts_per_window = 10

            if daily_limit is not None and daily_limit <= 0:
                self.logger.error(f"❌ 无效的每日限制参数：{daily_limit}，将忽略每日限制")
                daily_limit = None

            self.logger.info(f"🔧 调试：runtime_parameters.max_adds_per_window = {max_per_window}")
            self.logger.info(f"🔧 调试：runtime_parameters.daily_add_limit = {daily_limit}")
            self.logger.info(f"🔧 调试：multi_window.contacts_per_window = {self.config.get('multi_window', {}).get('contacts_per_window')}")
            self.logger.info(f"🎯 最终使用的数量限制：单窗口={contacts_per_window}个，每日={daily_limit}个")

            # 创建频率错误处理器实例
            frequency_handler = None
            try:
                from modules.frequency_error_handler import FrequencyErrorHandler
                frequency_handler = FrequencyErrorHandler(self.logger)
                self.logger.info("✅ 频率错误处理器初始化成功")
            except Exception as e:
                self.logger.warning(f"⚠️ 频率错误处理器初始化失败: {e}")

            for i, contact in enumerate(contacts, 1):
                try:
                    # 🔧 关键修复：在处理每个联系人前检查暂停状态
                    if not self._check_pause_status():
                        self.logger.info("⏸️ 联系人处理循环中检测到暂停，停止执行")
                        return False

                    # 🔧 关键修复：实时更新统计数据到主控制器
                    if self._controller:
                        self._controller.execution_stats["processed_contacts"] = i - 1
                        self._controller.execution_stats["successful_adds"] = success_count
                        self._controller.execution_stats["failed_adds"] = failed_count
                        self._controller._notify_gui_progress()

                    # 🆕 在处理每个联系人前立即检查频率错误状态
                    if frequency_handler and frequency_handler.is_restart_required():
                        self.logger.warning("🔄 联系人处理前检测到频率错误，立即停止流程")
                        self.logger.info("🚪 结束当前联系人处理循环，返回主控制器重新开始")

                        # 清除重新开始标志
                        frequency_handler.clear_restart_flag()

                        # 设置特殊返回状态，告知主控制器需要重新开始
                        self.stats["restart_required"] = True
                        self.stats["processed"] = i - 1  # 已处理的联系人数量
                        self.stats["success"] = success_count
                        self.stats["failed"] = failed_count
                        self.stats["end_time"] = datetime.now()

                        self.logger.info("🔄 返回主控制器，准备重新开始第一步")
                        return "RESTART_REQUIRED"  # 返回特殊状态码

                    # 🆕 动态参数响应 - 实时检查每日总数限制
                    current_runtime_params = self._get_realtime_runtime_params()
                    daily_limit = current_runtime_params.get("daily_add_limit", {}).get("value", None)

                    if daily_limit is not None and daily_limit > 0:
                        # 计算今日已处理总数（包括所有窗口）
                        total_processed_today = start_index + i - 1  # start_index是之前窗口处理的数量，i-1是当前窗口已处理的数量

                        self.logger.info(f"🔍 实时数量限制检查：当前已处理 {total_processed_today}/{daily_limit} 个联系人")

                        if total_processed_today >= daily_limit:
                            self.logger.info(f"🎯 已达到每日添加上限！今日已处理 {total_processed_today}/{daily_limit} 个联系人")
                            self.logger.info("🛑 程序自动终止，已完成今日任务")

                            # 设置完成状态
                            self.stats["processed"] = i - 1  # 当前窗口已处理的联系人数量
                            self.stats["success"] = success_count
                            self.stats["failed"] = failed_count
                            self.stats["end_time"] = datetime.now()
                            self.stats["daily_limit_reached"] = True
                            self.stats["completion_reason"] = f"已完成{total_processed_today}/{daily_limit}人，达到每日添加上限，程序自动终止"

                            self.logger.info("✅ 每日任务完成，程序正常结束")
                            return "DAILY_LIMIT_REACHED"  # 返回特殊状态码

                    self.logger.info(f"📋 处理进度: {i}/{len(contacts)}")

                    # 处理单个联系人
                    result = self.process_single_contact(contact)

                    # 🆕 检查联系人处理结果是否要求重新开始
                    if result.get("status") == "restart_required":
                        self.logger.warning("🔄 联系人处理检测到频率错误，需要重新开始流程")
                        self.logger.info("🚪 结束当前联系人处理循环，返回主控制器重新开始")

                        # 清除重新开始标志
                        if frequency_handler:
                            frequency_handler.clear_restart_flag()

                        # 设置特殊返回状态，告知主控制器需要重新开始
                        self.stats["restart_required"] = True
                        self.stats["processed"] = i - 1  # 已处理的联系人数量
                        self.stats["success"] = success_count
                        self.stats["failed"] = failed_count
                        self.stats["end_time"] = datetime.now()

                        self.logger.info("🔄 返回主控制器，准备重新开始第一步")
                        return "RESTART_REQUIRED"  # 返回特殊状态码

                    # 🆕 处理完单个联系人后立即检查频率错误状态
                    if frequency_handler and frequency_handler.is_restart_required():
                        self.logger.warning("🔄 联系人处理后检测到频率错误，立即停止流程")
                        self.logger.info("🚪 结束当前联系人处理循环，返回主控制器重新开始")

                        # 清除重新开始标志
                        frequency_handler.clear_restart_flag()

                        # 设置特殊返回状态，告知主控制器需要重新开始
                        self.stats["restart_required"] = True
                        self.stats["processed"] = i  # 当前联系人已处理完成
                        self.stats["success"] = success_count
                        self.stats["failed"] = failed_count
                        self.stats["end_time"] = datetime.now()

                        self.logger.info("🔄 返回主控制器，准备重新开始第一步")
                        return "RESTART_REQUIRED"  # 返回特殊状态码
                        self.stats["success"] = success_count
                        self.stats["failed"] = failed_count
                        self.stats["end_time"] = datetime.now()

                        self.logger.info("🔄 返回主控制器，准备重新开始第一步")
                        return "RESTART_REQUIRED"  # 返回特殊状态码

                    # 根据用户要求的4种情况判断状态和结果
                    result_message = result.get("message", "")
                    result_status = result.get("status", "")

                    # 1. 检测到频繁操作
                    if (result_status == "restart_required" or
                        "频繁" in result_message or "操作频繁" in result_message or
                        "请稍后再试" in result_message or "操作过于频繁" in result_message):
                        failed_count += 1
                        status = "失败"
                        final_result = "检测到操作频繁"

                    # 2. 添加朋友成功（点击确定后）
                    elif (result["success"] and
                          (result_status in ["success", "completed", "add_to_contacts"] or
                           "添加朋友成功" in result_message or "添加到通讯录" in result_message or
                           "好友申请已发送" in result_message)):
                        success_count += 1
                        status = "成功"
                        final_result = "添加朋友成功"

                        # 🔧 关键修复：成功添加好友后触发自动休息检查
                        rest_result = self._trigger_auto_rest()
                        if rest_result == "NO_WINDOWS_FOUND":
                            # 🆕 使用去重机制记录错误日志
                            self._log_with_deduplication("ERROR", f"❌ 自动休息后未找到微信窗口，终止添加好友流程")
                            self.stats["processed"] = i
                            self.stats["success"] = success_count
                            self.stats["failed"] = failed_count
                            self.stats["end_time"] = datetime.now()
                            self.stats["termination_reason"] = "自动休息后未找到微信窗口"
                            return "NO_WINDOWS_AFTER_REST"

                    # 3. 检测到发送消息、语音聊天、视频聊天（已是好友）
                    elif ("发送消息" in result_message or "语音聊天" in result_message or
                          "视频聊天" in result_message or "已经是好友" in result_message or
                          result_status == "already_friend"):
                        failed_count += 1
                        status = "失败"
                        final_result = "已是共同好友"

                    # 4. 检测到无法找到该用户
                    elif ("无法找到该用户" in result_message or "用户不存在" in result_message or
                          "请检查你填写的账号是否正确" in result_message or
                          result_status == "user_not_found"):
                        failed_count += 1
                        status = "失败"
                        final_result = "无法添加好友"

                    # 其他失败情况
                    else:
                        failed_count += 1
                        status = "失败"
                        final_result = "无法添加好友"

                    # 🔧 关键修复：每次处理完联系人后立即更新GUI统计数据 - 使用绝对值更新方法
                    if self._controller:
                        self._controller.update_execution_stats_absolute(i, success_count, failed_count)
                        self.logger.debug(f"📊 实时更新统计：当前窗口处理{i}个，成功{success_count}个，失败{failed_count}个")

                    # 更新Excel状态
                    self.update_excel_status(
                        contact["index"],
                        status,
                        final_result
                    )

                    # 🆕 严格执行控制 - 单窗口限制达到时切换窗口
                    current_runtime_params = self._get_realtime_runtime_params()
                    current_max_per_window = current_runtime_params.get("max_adds_per_window", {}).get("value", contacts_per_window)

                    if current_max_per_window is not None and current_max_per_window > 0:
                        # 🔧 修复：i是从1开始的计数，当i >= current_max_per_window时，表示已经处理完指定数量
                        # 例如：设置2人限制，i=1(第1个人), i=2(第2个人)，当i=2时已达到限制
                        if i >= current_max_per_window:
                            self.logger.info(f"🎯 已处理 {i} 个联系人，达到单窗口最大限制 ({current_max_per_window})")
                            self.logger.info("🔄 当前窗口联系人处理完成，准备切换到下一个微信窗口")
                            self.logger.info("📋 继续执行直到达到每日总限制")

                            # 设置完成状态 - 窗口切换
                            self.stats["processed"] = i  # 当前窗口已处理的联系人数量
                            self.stats["success"] = success_count
                            self.stats["failed"] = failed_count
                            self.stats["end_time"] = datetime.now()
                            self.stats["window_switch_required"] = True
                            self.stats["completion_reason"] = f"已完成{i}/{current_max_per_window}人，达到单窗口最大限制，切换到下一个窗口"

                            self.logger.info("✅ 单窗口任务完成，切换到下一个窗口")
                            return "WINDOW_SWITCH_REQUIRED"  # 🆕 返回窗口切换状态码，切换到下一个窗口

                    # 操作间延时 - 支持暂停检查
                    delay = self.config["delay_range"][0] + (
                        self.config["delay_range"][1] - self.config["delay_range"][0]
                    ) * 0.5

                    # 🔧 关键修复：在延时期间支持暂停检查
                    self._sleep_with_pause_check(delay)

                except Exception as e:
                    failed_count += 1
                    self.logger.error(f"❌ 处理联系人异常: {contact['phone']} - {e}")

                    # 🔧 关键修复：异常时也要立即更新GUI统计数据 - 使用绝对值更新方法
                    if self._controller:
                        self._controller.update_execution_stats_absolute(i, success_count, failed_count)
                        self.logger.debug(f"📊 异常后更新统计：当前窗口处理{i}个，成功{success_count}个，失败{failed_count}个")

                    # 更新Excel状态
                    self.update_excel_status(
                        contact["index"],
                        "失败",
                        "无法添加好友"
                    )

            # 7. 更新统计信息
            self.stats["processed"] = len(contacts)
            self.stats["success"] = success_count
            self.stats["failed"] = failed_count
            self.stats["end_time"] = datetime.now()

            # 8. 生成执行报告
            self.generate_report()

            self.logger.info("🎉 自动化流程执行完成!")

            # 9. 检查是否需要自动启动下一个程序
            if self.auto_start_next_program and success_count > 0:
                self.logger.info("\n" + "🔄" * 20)
                self.logger.info("🔄 准备自动启动下一个程序...")
                self.logger.info("🔄" * 20)
                self._start_next_program()

            return success_count > 0

        except Exception as e:
            self.logger.error(f"❌ 自动化流程执行失败: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def generate_report(self):
        """生成执行报告"""
        try:
            self.logger.info("\n" + "="*60)
            self.logger.info("📊 微信自动添加好友执行报告")
            self.logger.info("="*60)

            # 计算执行时间
            if self.stats["start_time"] and self.stats["end_time"]:
                duration = self.stats["end_time"] - self.stats["start_time"]
                duration_str = str(duration).split('.')[0]
            else:
                duration_str = "未知"

            # 计算成功率
            success_rate = (self.stats["success"] / self.stats["processed"] * 100) if self.stats["processed"] > 0 else 0

            self.logger.info(f"⏱️ 执行时间: {duration_str}")
            self.logger.info(f"📊 总联系人: {self.stats['total']} 个")
            self.logger.info(f"📋 已处理: {self.stats['processed']} 个")
            self.logger.info(f"✅ 成功: {self.stats['success']} 个")
            self.logger.info(f"❌ 失败: {self.stats['failed']} 个")
            self.logger.info(f"📈 成功率: {success_rate:.1f}%")
            self.logger.info("="*60)

        except Exception as e:
            self.logger.error(f"❌ 生成报告失败: {e}")

    def generate_completion_report(self):
        """生成全部完成报告"""
        try:
            self.logger.info("\n" + "="*60)
            self.logger.info("🎉 微信自动添加好友 - 全部完成报告")
            self.logger.info("="*60)

            self.logger.info(f"📊 总联系人数量: {self.stats['total']} 个")
            self.logger.info(f"✅ 已完成处理: {self.stats['processed']} 个")
            self.logger.info(f"📈 完成率: 100.0%")
            self.logger.info(f"🎯 状态: 所有数据已全部处理完成")
            self.logger.info(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info("="*60)
            self.logger.info("💡 提示: 如需重新处理失败的联系人，请在Excel中将其状态改为'待处理'")
            self.logger.info("="*60)

        except Exception as e:
            self.logger.error(f"❌ 生成完成报告失败: {e}")

    def _handle_add_friend_window_specifically(self, hwnd: int) -> bool:
        """专门处理添加朋友窗口 - 新增方法"""
        try:
            self.logger.info("🎯 开始添加朋友窗口专门处理...")

            # 使用窗口管理器的专门方法
            try:
                from modules.window_manager import WeChatWindowManager
                window_manager = WeChatWindowManager()

                # 确保窗口可见
                ensure_visible_method = getattr(window_manager, 'ensure_add_friend_window_visible', None)
                if ensure_visible_method:
                    if ensure_visible_method(hwnd):
                        self.logger.info("✅ 添加朋友窗口可见性确保成功")
                    else:
                        self.logger.warning("⚠️ 添加朋友窗口可见性确保失败")
                else:
                    self.logger.warning("⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法")

                # 启动持续监控
                monitoring_method = getattr(window_manager, '_start_add_friend_window_monitoring', None)
                if monitoring_method:
                    monitoring_method(hwnd)
                    self.logger.info("✅ 添加朋友窗口监控已启动")
                else:
                    self.logger.warning("⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法")

                return True

            except ImportError:
                self.logger.warning("⚠️ 无法导入窗口管理器，跳过专门处理")
                return False

        except Exception as e:
            self.logger.error(f"❌ 添加朋友窗口专门处理失败: {e}")
            return False

    def wait_for_add_friend_window_and_activate(self, timeout: int = 10) -> bool:
        """等待添加朋友窗口出现并激活 - 新增方法"""
        try:
            self.logger.info(f"⏳ 等待添加朋友窗口出现并激活（超时: {timeout}秒）...")

            # 使用窗口管理器等待
            try:
                from modules.window_manager import WeChatWindowManager
                window_manager = WeChatWindowManager()

                wait_method = getattr(window_manager, 'wait_for_add_friend_window', None)
                if wait_method:
                    add_friend_hwnd = wait_method(timeout)

                    if add_friend_hwnd:
                        self.logger.info("✅ 添加朋友窗口已找到并激活")
                        self.add_friend_window = {"hwnd": add_friend_hwnd}
                        return True
                    else:
                        self.logger.warning("⚠️ 添加朋友窗口未在指定时间内出现")
                        return False
                else:
                    self.logger.warning("⚠️ 窗口管理器不支持wait_for_add_friend_window方法")
                    return False

            except ImportError:
                self.logger.warning("⚠️ 无法导入窗口管理器，使用传统等待方法")
                time.sleep(timeout)
                return False

        except Exception as e:
            self.logger.error(f"❌ 等待添加朋友窗口失败: {e}")
            return False

    def _start_next_program(self):
        """启动下一个程序 (wechat_auto_add_friend.py)"""
        try:
            self.logger.info("🚀 开始启动下一个程序...")

            # 如果有自定义回调函数，使用回调函数
            if self.next_program_callback:
                self.logger.info("📞 使用自定义回调函数启动下一个程序")
                try:
                    result = self.next_program_callback()
                    if result:
                        self.logger.info("✅ 通过回调函数成功启动下一个程序")
                    else:
                        self.logger.error("❌ 通过回调函数启动下一个程序失败")
                except Exception as e:
                    self.logger.error(f"❌ 回调函数执行异常: {e}")
                return

            # 默认启动方式：直接导入并执行
            self.logger.info("📦 使用默认方式启动下一个程序")
            self._start_add_friend_program_default()

        except Exception as e:
            self.logger.error(f"❌ 启动下一个程序异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

    def _start_add_friend_program_default(self):
        """默认方式启动添加朋友程序"""
        try:
            self.logger.info("📋 准备启动 wechat_auto_add_friend.py 程序...")

            # 添加程序间的延迟，确保界面稳定
            transition_delay = 3.0
            self.logger.info(f"⏳ 程序间延迟 {transition_delay} 秒，确保界面稳定...")
            time.sleep(transition_delay)

            # 动态导入添加朋友模块
            try:
                from modules.wechat_auto_add_friend import WeChatAutoAddFriend as AddFriendModule
                self.logger.info("✅ 成功导入添加朋友模块")
            except ImportError as e:
                self.logger.error(f"❌ 导入添加朋友模块失败: {e}")
                return False

            # 初始化添加朋友实例
            self.logger.info("🔧 正在初始化添加朋友模块...")
            add_friend_instance = AddFriendModule(debug_mode=True)

            # 清理截图目录
            self.logger.info("🧹 清理截图目录...")
            add_friend_instance.clean_screenshots_directory()

            # 执行添加朋友操作
            self.logger.info("🚀 启动添加朋友操作...")
            self.logger.info("📋 功能说明:")
            self.logger.info("  - 自动检测微信'添加朋友'窗口")
            self.logger.info("  - 图像识别'添加到通讯录'文字")
            self.logger.info("  - 点击'添加到通讯录'按钮")
            self.logger.info("  - 保存截图和日志到本地")

            # 执行自动添加朋友操作
            success = add_friend_instance.execute_auto_add_friend()

            if success:
                self.logger.info("✅ 添加朋友程序执行成功")
                self.logger.info("🎉 完整的自动化流程执行完成！")
            else:
                self.logger.error("❌ 添加朋友程序执行失败")
                self.logger.info("💡 可能的原因:")
                self.logger.info("  - 未找到'添加朋友'窗口")
                self.logger.info("  - 未找到'添加到通讯录'按钮")
                self.logger.info("  - 用户不存在或已经是好友")
                self.logger.info("  - 网络连接问题")

            return success

        except Exception as e:
            self.logger.error(f"❌ 默认启动添加朋友程序异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _execute_main_interface_operations(self) -> bool:
        """执行主界面操作来打开添加朋友窗口"""
        try:
            self.logger.info("🚀 开始执行主界面操作流程...")
            self.logger.info("📋 目标：从微信主窗口打开添加朋友窗口")

            # 导入主界面操作模块
            try:
                from modules.main_interface import WeChatMainInterface
                self.logger.info("📦 成功导入主界面操作模块")
            except ImportError as e:
                self.logger.error(f"❌ 导入主界面操作模块失败: {e}")
                return False

            # 创建主界面操作实例
            try:
                main_interface = WeChatMainInterface("config.json")
                self.logger.info("🔧 主界面操作模块初始化成功")
            except Exception as e:
                self.logger.error(f"❌ 主界面操作模块初始化失败: {e}")
                return False

            # 执行主界面操作流程
            self.logger.info("🎯 执行主界面操作流程...")
            success = main_interface.execute_main_interface_flow()

            if success:
                self.logger.info("✅ 主界面操作流程执行成功")
                self.logger.info("✅ 应该已经打开了添加朋友窗口")
                return True
            else:
                self.logger.error("❌ 主界面操作流程执行失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 执行主界面操作异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False


def main():
    """主函数 - 全自动执行"""
    try:
        print("🚀 微信自动添加好友脚本启动...")
        print("📋 功能说明:")
        print("  1. 自动查找微信窗口")
        print("  2. 激活窗口并移动到(0,0)位置")
        print("  3. 从Excel文件读取手机号码")
        print("  4. 自动在搜索框输入手机号并搜索")
        print("  5. 更新处理状态到Excel文件")
        print()

        # 确认Excel文件路径
        excel_file = "添加好友名单.xlsx"
        if not os.path.exists(excel_file):
            print(f"❌ Excel文件不存在: {excel_file}")
            print("请确保文件存在于当前目录中")
            return

        print(f"📂 Excel文件: {excel_file}")
        print("🤖 全自动模式，3秒后开始执行...")

        # 倒计时
        for i in range(3, 0, -1):
            print(f"⏰ {i}秒后开始...")
            time.sleep(1)

        print("🚀 开始执行自动化流程...")

        # 创建自动化实例并运行
        automation = WeChatAutoAddFriend(excel_file)

        # 启用自动启动下一个程序功能
        automation.set_auto_start_next_program()

        success = automation.run_automation()

        if success:
            print("\n🎉 自动化流程执行完成!")
            print("📊 详细结果请查看:")
            print(f"  - Excel文件: {excel_file}")
            print("  - 日志文件: logs/目录")
        else:
            print("\n❌ 自动化流程执行失败")
            print("请检查日志文件获取详细错误信息")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
